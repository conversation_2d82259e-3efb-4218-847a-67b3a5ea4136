import type { TripDetail } from '../trip-detail/types';

export const TripDetailMock: TripDetail = {
    id: 194940,
    transitNumber: 'T123',
    status: 'Active',
    trackingStatus: 'Started',
    vehicle: { id: 42, plateNo: 'ABC121559699' },
    driver: { id: 42, name: 'dimo', mobileNo: '1234968863', passportCountry: 'Saudi Arabia' },
    routeId: 21,
    entryPort: { id: 1, name: { arabic: 'جدة', english: 'Jeddah' } },
    exitPort: { id: 2, name: { arabic: ' الرياض', english: 'Riyadh' } },
    startDate: '2025-08-18T07:46:49.5847239',
    endDate: null,
    tracker: { id: 2, serialNumber: '7798EA46' },
    eLocks: [{ deviceId: 24, tripELockId: 1 }],
    trackingPriority: 'Medium',
    transitDate: '30-1-1444',
    transSeq: 'V123',
    shipmentDescription: 'ShipmentDescription',
    newShipmentDescription: null,
    transitTypeName: 'TransitTypeName',
    ownerDesc: 'OwnerDescription',
    lastKnownLocation: {
        latitude: 24.7136,
        longitude: 46.7083,
        angle: 90,
        formattedAddress: 'Riyadh, Saudi Arabia',
        trackerDateTime: '2025-08-19T01:11:51.328848+03:00',
        source: 'Exact',
    },
    activeAlerts: [
        {
            alertStateChangeId: 5001,
            alertType: {
                id: 1,
                name: { arabic: 'تنبيه السرعة', english: 'Overspeed Alert' },
                alertPriority: 1,
                origin: 1,
                alertCode: 1001,
            },
            fromState: { stateId: 10, timestamp: '2025-08-28T12:05:00Z' },
            toState: { stateId: 20, timestamp: '2025-08-28T12:06:00Z' },
        },
        {
            alertStateChangeId: 5002,
            alertType: {
                id: 2,
                name: { arabic: 'تنبيه جغرافي', english: 'Geofence Alert' },
                alertPriority: 2,
                origin: 2,
                alertCode: 2001,
            },
            fromState: { stateId: 30, timestamp: '2025-08-28T13:00:00Z' },
            toState: null, // allowed
        },
    ],
    currentState: {
        trackerDateTime: '2025-08-19T01:07:02.910231+03:00',
        locationSource: 'Exact',
        batteryLevelPercentage: 85,
        chargerStatus: 'Unknown',
        gpsSignalStrength: 4,
        gsmSignalStrength: 4,
        currentSpeed: 60,
        routeZone: 'Entry',
        isWithinRouteGeofence: true,
        timeElapsedSinceTripStartInMinutes: 15,
        remainingDistanceInMeters: 12000,
        isWithinSuspiciousZone: false,
        createdAt: '2025-08-19T01:07:02.910231+03:00',
    },
};

// ✅ Another mock trip with active alerts
export const TripDetailWithAlertsMock: TripDetail = {
    id: 194941,
    transitNumber: 'T124',
    status: 'Active',
    trackingStatus: 'Active',
    vehicle: { id: 43, plateNo: 'XYZ998877' },
    driver: { id: 43, name: 'Sara Ali', mobileNo: '9876543210', passportCountry: 'Saudi Arabia' },
    routeId: 22,
    entryPort: { id: 1, name: { arabic: 'جدة', english: 'Jeddah' } },
    exitPort: { id: 2, name: { arabic: ' الرياض', english: 'Riyadh' } },
    startDate: '2025-08-19T09:30:00.000Z',
    endDate: null,
    tracker: { id: 3, serialNumber: 'AA12BB34' },
    eLocks: [
        { deviceId: 25, tripELockId: 2 },
        { deviceId: 26, tripELockId: 3 },
    ],
    trackingPriority: 'High',
    transitDate: '1-2-1446',
    transSeq: 'V124',
    shipmentDescription: 'Sensitive goods',
    newShipmentDescription: 'Updated goods description',
    transitTypeName: 'International',
    ownerDesc: 'Owner XYZ',
    lastKnownLocation: {
        latitude: 21.3891,
        longitude: 39.8579,
        angle: 180,
        formattedAddress: 'Jeddah, Saudi Arabia',
        trackerDateTime: '2025-08-19T10:15:00.000Z',
        source: 'GPS',
    },
    activeAlerts: [
        {
            alertStateChangeId: 5001,
            alertType: {
                id: 1,
                name: { arabic: 'تنبيه السرعة', english: 'Overspeed Alert' },
                alertPriority: 1,
                origin: 1,
                alertCode: 1001,
            },
            fromState: { stateId: 10, timestamp: '2025-08-28T12:05:00Z' },
            toState: { stateId: 20, timestamp: '2025-08-28T12:06:00Z' },
        },
        {
            alertStateChangeId: 5002,
            alertType: {
                id: 2,
                name: { arabic: 'تنبيه جغرافي', english: 'Geofence Alert' },
                alertPriority: 2,
                origin: 2,
                alertCode: 2001,
            },
            fromState: { stateId: 30, timestamp: '2025-08-28T13:00:00Z' },
            toState: null, // allowed
        },
    ],
    currentState: {
        trackerDateTime: '2025-08-19T10:10:00.000Z',
        locationSource: 'GPS',
        batteryLevelPercentage: 60,
        chargerStatus: 'Charging',
        gpsSignalStrength: 3,
        gsmSignalStrength: 2,
        currentSpeed: 45,
        routeZone: 'Transit',
        isWithinRouteGeofence: false,
        timeElapsedSinceTripStartInMinutes: 35,
        remainingDistanceInMeters: 50000,
        isWithinSuspiciousZone: true,
        createdAt: '2025-08-19T10:10:00.000Z',
    },
};
