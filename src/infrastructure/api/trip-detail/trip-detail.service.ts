import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app-settings.config';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';

import { TripDetailMock } from '../mock-data/trip-detail.mock'; // 👈 mock data import

import { TripDetailSchema } from './schemas';
import type { GetTripDetailQueryParams, TripDetail } from './types';

export class TripDetailService {
    private static instance: TripDetailService;

    private constructor() {}

    public static getInstance(): TripDetailService {
        if (!TripDetailService.instance) {
            TripDetailService.instance = new TripDetailService();
        }
        return TripDetailService.instance;
    }

    public async getTripDetail(params: GetTripDetailQueryParams): Promise<TripDetail | null> {
        logger.info('[TripDetailService] fetching trip detail with ID: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchTripDetail(params);
            }
            return this.realFetchTripDetail(params);
        } catch (error: unknown) {
            logger.error('[TripDetailService] Error fetching trip detail: ', error as Error);
            throw error;
        }
    }

    private async fakeFetchTripDetail(params: Partial<GetTripDetailQueryParams> = {}): Promise<TripDetail> {
        logger.info('[TripDetailService] returning mocked trip detail for ID:', params ?? {});
        return Promise.resolve(TripDetailMock);
    }

    private async realFetchTripDetail(params: Partial<GetTripDetailQueryParams> = {}): Promise<TripDetail | null> {
        const response = await fetchy.get<TripDetail>(`trips/${params.id}`);

        const validationResult = valy.validate(TripDetailSchema, response.data, 'trip_detail');
        if (validationResult.success === false) {
            logger.error(
                '[TripDetailService] Invalid response from trip detail API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return null;
        }

        return validationResult.data as TripDetail;
    }
}
