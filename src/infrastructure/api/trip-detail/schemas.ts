import { z } from 'zod';

import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';

import { AlertTypeSchema } from '../alert-types/schemas';

export const PortSchema = z.object({
    id: z.number(),
    name: TranslatableSchema,
});

export const StateChangeSchema = z.object({
    stateId: z.number(),
    timestamp: z.string(),
});

export const AlertSchema = z.object({
    alertStateChangeId: z.number(),
    alertType: AlertTypeSchema,
    fromState: StateChangeSchema,
    toState: StateChangeSchema.nullable(),
});

export const GetTripDetailQueryParamsSchema = z.object({
    id: z.number(),
});
export const TripDetailSchema = z.object({
    id: z.number(),
    transitNumber: z.string(),
    status: z.string(),
    trackingStatus: z.string(),
    vehicle: z.object({
        id: z.number(),
        plateNo: z.string(),
    }),
    driver: z.object({
        id: z.number(),
        name: z.string(),
        mobileNo: z.string(),
        passportCountry: z.string(),
    }),
    routeId: z.number().nullable(),
    entryPort: PortSchema.nullable(),
    exitPort: PortSchema.nullable(),
    startDate: z.string(),
    endDate: z.string().nullable(),
    tracker: z.object({
        id: z.number(),
        serialNumber: z.string(),
    }),
    eLocks: z.array(
        z.object({
            deviceId: z.number(),
            tripELockId: z.number(),
        }),
    ),
    trackingPriority: z.string(),
    transitDate: z.string(),
    transSeq: z.string(),
    shipmentDescription: z.string().nullable(),
    newShipmentDescription: z.string().nullable(),
    transitTypeName: z.string(),
    ownerDesc: z.string(),
    lastKnownLocation: z
        .object({
            latitude: z.number(),
            longitude: z.number(),
            angle: z.number(),
            formattedAddress: z.string(),
            trackerDateTime: z.string(),
            source: z.string(),
        })
        .nullable(),
    activeAlerts: z.array(AlertSchema),
    currentState: z
        .object({
            trackerDateTime: z.string(),
            locationSource: z.string(),
            batteryLevelPercentage: z.number(),
            chargerStatus: z.string(),
            gpsSignalStrength: z.number(),
            gsmSignalStrength: z.number(),
            currentSpeed: z.number(),
            routeZone: z.string(),
            isWithinRouteGeofence: z.boolean(),
            timeElapsedSinceTripStartInMinutes: z.number(),
            remainingDistanceInMeters: z.number(),
            isWithinSuspiciousZone: z.boolean(),
            createdAt: z.string(),
        })
        .nullable(),
});
