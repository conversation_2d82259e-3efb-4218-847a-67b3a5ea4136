import type { z } from 'zod';

import type {
    AlertSchema,
    GetTripDetailQueryParamsSchema,
    PortSchema,
    StateChangeSchema,
    TripDetailSchema,
} from './schemas';

export type TripDetail = z.infer<typeof TripDetailSchema>;
export type GetTripDetailQueryParams = z.infer<typeof GetTripDetailQueryParamsSchema>;
export type Port = z.infer<typeof PortSchema>;
export type Alert = z.infer<typeof AlertSchema>;
export type StateChange = z.infer<typeof StateChangeSchema>;
