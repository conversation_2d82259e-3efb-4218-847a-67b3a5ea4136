import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';
import { TripLocation, TrackingStatus, TripStatus } from '@/shared/enums';

export const TrackingStatusSchema = z.enum(TrackingStatus);
export const TripStatusSchema = z.enum(TripStatus);
export const RouteZoneSchema = z.enum(TripLocation);

export const TrackerDtoSchema = z.object({
    id: z.coerce.number(),
    serialNumber: z.string(),
});

export const VehicleDtoSchema = z.object({
    id: z.coerce.number(),
    plateNo: z.string().nullable(),
    model: z.string().nullable(),
    type: z.string().nullable(),
    color: z.string().nullable(),
    plateCountryName: z.string().nullable(),
    plateCountryCode: z.string().nullable(),
});

export const DriverDtoSchema = z.object({
    id: z.coerce.number(),
    name: z.string().nullable(),
    mobileNo: z.string().nullable(),
});

export const PortDtoSchema = z.object({
    id: z.coerce.number(),
    code: z.string(),
    name: TranslatableSchema,
});

export const AlertTypeDtoSchema = z.object({
    id: z.coerce.number(),
    name: TranslatableSchema,
});

export const AlertDtoSchema = z.object({
    alertStateChangeId: z.coerce.number(),
    alertType: AlertTypeDtoSchema,
});

export const ELockDtoSchema = z.object({
    deviceId: z.coerce.number(),
    tripELockId: z.coerce.number(),
    serialNumber: z.string(),
});

export const CurrentStateDtoSchema = z.object({
    trackerDateTime: z.coerce.date().transform((date) => date.toISOString()), // Coerce to Date then transform to ISO string
    locationSource: z.coerce.number(),
    batteryLevelPercentage: z.coerce.number(),
    chargerStatus: z.coerce.number(),
    gpsSignalStrength: z.coerce.number(),
    gsmSignalStrength: z.coerce.number(),
    currentSpeed: z.coerce.number(),
    routeZone: z.coerce.number(),
    isWithinRouteGeofence: z.coerce.boolean(),
    timeElapsedSinceTripStartInMinutes: z.coerce.number(),
    remainingDistanceInMeters: z.coerce.number(),
    isWithinSuspiciousZone: z.coerce.boolean(),
    createdAt: z.coerce.date().transform((date) => date.toISOString()),
});

// Main data item schema
export const TripsItemSchema = z.object({
    id: z.coerce.number(),
    transitNumber: z.string().nullable(),
    status: TripStatusSchema.nullable().default(null),
    trackingStatus: TrackingStatusSchema.nullable().default(null),
    vehicle: VehicleDtoSchema,
    driver: DriverDtoSchema,
    routeId: z.coerce.number(),
    entryPort: PortDtoSchema,
    exitPort: PortDtoSchema,
    startDate: z.coerce.date().transform((date) => date.toISOString()),
    endDate: z.coerce
        .date()
        .transform((date) => date.toISOString())
        .nullable(),
    tracker: TrackerDtoSchema,
    transitDate: z.string().nullable(),
    transSeq: z.string().nullable(),
    shipmentDescription: z.string().nullable(),
    newShipmentDescription: z.string().nullable(),
    transitTypeName: z.string().nullable(),
    ownerDesc: z.string().nullable(),
    activeAlert: AlertDtoSchema.nullable(),
    currentState: CurrentStateDtoSchema.nullable(),
    eLocks: z.array(ELockDtoSchema),
    lastKnownRouteZone: RouteZoneSchema.nullable().default(null),
});

// Final response schema
export const TripsApiResponseSchema = z.object({
    data: z.array(TripsItemSchema),
    pagination: PaginationSchema,
});
