import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { appConfig } from '@/shared/config/app-settings.config';

import { TripsData } from '../mock-data/trips';

import { TripsApiResponseSchema } from './schemas';
import type { TripsRequest, TripsResponse } from './types';

export class TripsLService {
    private static instance: TripsLService;

    private constructor() {}

    public static getInstance(): TripsLService {
        if (!TripsLService.instance) {
            TripsLService.instance = new TripsLService();
        }
        return TripsLService.instance;
    }

    public async getTrips(params: TripsRequest): Promise<TripsResponse> {
        logger.info('[TripsLService] fetching trips with params: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchTrips(params);
            }
            return this.realFetchTrips(params);
        } catch (error: unknown) {
            logger.error('[TripsLService] Error fetching trips: ', error as Error);
            throw error;
        }
    }

    private fakeFetchTrips(params: TripsRequest): Promise<TripsResponse> {
        logger.info('[TripsLService] fetching trips with params: ', params);
        return Promise.resolve(TripsData);
    }

    private async realFetchTrips(params: TripsRequest): Promise<TripsResponse> {
        const response = await fetchy.post<TripsResponse>('/trips', { body: params });
        // todo cache using react-query

        const validationResult = valy.validate(TripsApiResponseSchema, response.data, 'trips_response');
        if (validationResult.success === false) {
            logger.error(
                '[TripsLService] Invalid response from trips API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return TripsData; // Return mock data as fallback
        }

        return validationResult.data as TripsResponse;
    }
}

export const tripsService = TripsLService.getInstance();
