import type { z } from 'zod';

import type { TripLocationRequestSchema } from '../trips-location/schemas';

import type { TripsApiResponseSchema, TripsItemSchema } from './schemas';

export type TripsItem = z.infer<typeof TripsItemSchema>;
export type TripsResponse = z.infer<typeof TripsApiResponseSchema>;
export type TripsRequest = z.infer<typeof TripLocationRequestSchema>;

// TODO: remove the following unused types
// import type { TranslatableSchema } from '@/infrastructure/validation/schemas/translatableSchema';
// import type {
//     CurrentStateDtoSchema,
//     ELockDtoSchema,
//     AlertDtoSchema,
//     AlertTypeDtoSchema,
//     PortDtoSchema,
//     DriverDtoSchema,
//     VehicleDtoSchema,
//     TrackerDtoSchema,
//     RouteZoneSchema,
//     TrackingStatusSchema,
//     TripStatusSchema,
// } from './schemas';
// Export types for use in TypeScript
// export type TripStatus = z.infer<typeof TripStatusSchema>;
// export type TrackingStatus = z.infer<typeof TrackingStatusSchema>;
// export type RouteZone = z.infer<typeof RouteZoneSchema>;
// export type Translatable = z.infer<typeof TranslatableSchema>;
// export type TrackerDto = z.infer<typeof TrackerDtoSchema>;
// export type VehicleDto = z.infer<typeof VehicleDtoSchema>;
// export type DriverDto = z.infer<typeof DriverDtoSchema>;
// export type PortDto = z.infer<typeof PortDtoSchema>;
// export type AlertTypeDto = z.infer<typeof AlertTypeDtoSchema>;
// export type AlertDto = z.infer<typeof AlertDtoSchema>;
// export type ELockDto = z.infer<typeof ELockDtoSchema>;
// export type CurrentStateDto = z.infer<typeof CurrentStateDtoSchema>;
