import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { appConfig } from '@/shared/config/app-settings.config';

import { TripLocationData } from '../mock-data/location.mock';

import { TripLocationResponseSchema } from './schemas';
import type { TripLocationRequest, TripLocationResponse } from './types';

export class TripsLocationService {
    private static instance: TripsLocationService;

    private constructor() {}

    public static getInstance(): TripsLocationService {
        if (!TripsLocationService.instance) {
            TripsLocationService.instance = new TripsLocationService();
        }
        return TripsLocationService.instance;
    }

    public async getTripLocations(params: TripLocationRequest): Promise<TripLocationResponse | null> {
        logger.info('[TripsLocationService] fetching trip locations with params: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchTripLocations(params);
            }
            return this.realFetchTripLocations(params);
        } catch (error: unknown) {
            logger.error('[TripsLocationService] Error fetching trip locations: ', error as Error);
            throw error;
        }
    }

    private fakeFetchTripLocations(params: TripLocationRequest): Promise<TripLocationResponse> {
        logger.info('[TripsLocationService] fetching trip locations with params: ', params);
        return Promise.resolve(TripLocationData);
    }

    private async realFetchTripLocations(params: TripLocationRequest): Promise<TripLocationResponse | null> {
        const response = await fetchy.post<TripLocationResponse>('/trips/location', { body: params });
        // todo cache using react-query

        if (response.status !== 200) {
            logger.error(
                '[TripsLocationService] Invalid response from trip locations API',
                new Error(`api return with error status ${response.status}`),
            );
            return null;
        }

        const validationResult = valy.validate(TripLocationResponseSchema, response.data, 'trip_location_response');
        if (validationResult.success === false) {
            logger.error(
                '[TripsLocationService] Invalid response from trip locations API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return TripLocationData; // Return mock data as fallback
        }

        return validationResult.data as TripLocationResponse;
    }
}

export const tripLocationService = TripsLocationService.getInstance();
