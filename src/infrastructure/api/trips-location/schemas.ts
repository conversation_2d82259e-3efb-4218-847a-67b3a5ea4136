import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TripLocation, OrderDirection, TripFilterOrderBy, TripCategory, AlertAcknowledgement } from '@/shared/enums';

export const TripLocationRequestSchema = z.object({
    inPorts: z.array(z.number()).default([]),
    outPorts: z.array(z.number()).default([]),
    alertTypes: z.array(z.number()).default([]),
    activeAlertsOnly: z.boolean().nullable().default(null),
    transitNumber: z.string().nullable().default(null),
    transitSeqNumber: z.string().nullable().default(null),
    driverName: z.string().nullable().default(null),
    plateNumber: z.string().nullable().default(null),
    trackerNumber: z.string().nullable().default(null),
    tipCode: z.string().nullable().default(null),
    tripCategory: z.enum(TripCategory).nullable().default(null),
    tripLocations: z.array(z.enum(TripLocation)).default([]),
    activeTripsOnly: z.boolean().nullable().default(null),
    tripStartDate: z.date().nullable().default(null),
    tripEndDate: z.date().nullable().default(null),
    transDate: z.date().nullable().default(null),
    orderBy: z.enum(TripFilterOrderBy).nullable().default(null),
    orderDir: z.enum(OrderDirection).nullable().default(null),
    alertAcknowledgement: AlertAcknowledgement,
    pageSize: z.number().min(1).default(1000),
    pageNumber: z.number().min(1).default(1),
});

export const TripLocationSchema = z.object({
    id: z.number(),
    currentLocation: z.object({
        lat: z.number(),
        long: z.number(),
    }),
    angle: z.number(),
});

export const TripLocationResponseSchema = z.object({
    data: z.array(TripLocationSchema),
    pagination: PaginationSchema,
});
