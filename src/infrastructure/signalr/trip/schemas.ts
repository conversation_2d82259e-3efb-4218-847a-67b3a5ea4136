import { z } from 'zod';

import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';
import { PortSchema } from '@/infrastructure/api/trip-detail/schemas';
import { LocationSource, ChargerStatus, EventType, TripLocation } from '@/shared/enums';

export const AlertStateDtoSchema = z.object({
    id: z.number(),
    dateTime: z.coerce.date(),
    locationSource: z.enum(LocationSource),
    batteryLevelPercentage: z.number().int().min(0).max(100),
    chargerStatus: z.enum(ChargerStatus),
    gpsSignalStrength: z.number().int().min(0).max(100),
    gsmSignalStrength: z.number().int().min(0).max(100),
    currentSpeed: z.number().int().min(0),
    routeZone: z.enum(TripLocation),
    isWithinRouteGeofence: z.boolean(),
    timeElapsedSinceTripStartInMinutes: z.number().int().min(0),
    remainingDistanceInMeters: z.number().int().min(0),
    isWithinSuspiciousZone: z.boolean(),
    trackerDateTime: z.coerce.date(),
});

export const AlertMessageSchema = z.object({
    tripId: z.number().int(),
    alertType: z.object({
        id: z.number().int(),
        name: TranslatableSchema,
    }),
    transitNumber: z.string(),
    entryPort: PortSchema,
    exitPort: PortSchema,
    fromStateDateTime: z.coerce.date(),
    toStateDateTime: z.coerce.date().nullable().optional(),
    currentState: AlertStateDtoSchema,
});

export const TripUpdateMessageSchema = z.object({
    tripId: z.number().int(),
    message: z.string(),
    eventType: z.enum(EventType),
});
