import * as React from 'react';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { cn } from '@/shared/utils/class-name.utils';

interface FullscreenDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    children: React.ReactNode;
}

/**
 * FullscreenDialog - A dialog component that renders inline without portals
 * This is specifically designed to work within Google Maps fullscreen mode
 * where portal-based dialogs don't appear.
 */
export function FullscreenDialog({ open, onOpenChange, children }: FullscreenDialogProps) {
    const { i18n } = useTranslation();
    const language = i18n.language;

    // Handle escape key
    React.useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && open) {
                onOpenChange(false);
            }
        };

        if (open) {
            document.addEventListener('keydown', handleEscape);
            // Prevent body scroll when dialog is open
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [open, onOpenChange]);

    if (!open) return null;

    return (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center">
            {/* Overlay */}
            <div
                className="fixed inset-0 bg-black/80 animate-in fade-in-0"
                onClick={() => onOpenChange(false)}
            />
            
            {/* Content */}
            <div
                className={cn(
                    'relative z-[10000] grid w-full max-w-lg gap-4 border bg-background p-6 shadow-lg duration-200 animate-in fade-in-0 zoom-in-95 slide-in-from-left-1/2 slide-in-from-top-[48%] sm:rounded-lg',
                )}
                onClick={(e) => e.stopPropagation()}
            >
                {children}
                <button
                    className={cn(
                        'absolute rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none',
                        language === 'ar' ? 'left-4 top-4' : 'right-4 top-4',
                    )}
                    onClick={() => onOpenChange(false)}
                >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close</span>
                </button>
            </div>
        </div>
    );
}

export const FullscreenDialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
    <div className={cn('flex flex-col space-y-1.5 text-center sm:text-left', className)} {...props} />
);
FullscreenDialogHeader.displayName = 'FullscreenDialogHeader';

export const FullscreenDialogTitle = React.forwardRef<
    HTMLHeadingElement,
    React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
    <h2
        ref={ref}
        className={cn('text-lg font-semibold leading-none tracking-tight', className)}
        {...props}
    />
));
FullscreenDialogTitle.displayName = 'FullscreenDialogTitle';

export const FullscreenDialogDescription = React.forwardRef<
    HTMLParagraphElement,
    React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
    <p ref={ref} className={cn('text-sm text-muted-foreground', className)} {...props} />
));
FullscreenDialogDescription.displayName = 'FullscreenDialogDescription';
