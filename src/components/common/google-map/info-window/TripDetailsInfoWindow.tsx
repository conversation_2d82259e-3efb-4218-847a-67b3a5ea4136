import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Info } from 'lucide-react';

import { useTripDetailStore } from '@/stores/trip-detail.store';
import { getTripStatusConditions } from '@/shared/utils/trip-status.utils';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { formatLocalizedDate } from '@/shared/utils/format-date.utils';

import type { MapTrip } from '../GoogleMap';
import { Button } from '../../ui/Button';
import { Tag } from '../../ui/Tag';
import Tooltip from '../../ui/Tooltip';
import { TooltipTrigger } from '../../ui/LibTooltip';

import { BaseInfoWindow } from './BaseInfoWindow';
import { TripAlertCarousel } from './TripAlertCarousel';
import { TripSourceDestination } from './TripSourceDestination';

type TripDetailsInfoWindowProps = {
    trip: MapTrip | null;
    onClose: () => void;
};

function DetailRow({ label, value }: { label: string; value: React.ReactNode }) {
    return (
        <div className="table-row text-xs">
            <span className="table-cell font-medium text-gray-500 whitespace-nowrap rtl:pl-2 ltr:pr-2">{label}</span>
            <span className="table-cell text-gray-700 truncate font-bold">{value}</span>
        </div>
    );
}

export function TripDetailsInfoWindow({ trip, onClose }: TripDetailsInfoWindowProps) {
    const { t } = useTranslation();
    const { tripDetail, isLoading, loadTripDetail } = useTripDetailStore();
    const statusConditions = tripDetail ? getTripStatusConditions(tripDetail) : [];
    const localized = useLocalized();

    useEffect(() => {
        if (trip) loadTripDetail({ id: trip.id });
    }, [trip, loadTripDetail]);

    if (!trip) return null;

    const position = { lat: trip.currentLocation.lat, lng: trip.currentLocation.long };
    const title = `${tripDetail?.transitNumber ?? 'N/A'}/\u200E#${trip.id}`;
    const subtitle = formatLocalizedDate(tripDetail?.startDate);

    // ================== (2) ALERTS SECTION ==================
    const alertsSection = !isLoading && tripDetail && tripDetail.activeAlerts && tripDetail.activeAlerts.length > 0 && (
        <div>
            {/* Pass activeAlerts to carousel */}
            <TripAlertCarousel alerts={tripDetail.activeAlerts} />
        </div>
    );

    // ================== (3) FROM–TO SECTION ==================

    const fromToSection = tripDetail && (
        <div>
            <TripSourceDestination
                fromLabel={tripDetail.entryPort?.name ? localized(tripDetail.entryPort.name) : 'N/A'}
                toLabel={tripDetail.exitPort?.name ? localized(tripDetail.exitPort.name) : 'N/A'}
            />

            {/* Started / Ended dates stacked */}
            <div className="flex justify-between text-gray-500 mt-2">
                {/* Started */}
                <div className="flex flex-col items-start">
                    <span>{t('tripDetails.startedAtDate')}</span>
                    <span className="font-bold">{formatLocalizedDate(tripDetail.startDate)}</span>
                </div>

                {/* Ended */}
                <div className="flex flex-col items-end">
                    <span>{t('tripDetails.endedAtDate')}</span>
                    <span className="font-bold">
                        {tripDetail.endDate ? formatLocalizedDate(tripDetail.endDate) : '-'}
                    </span>
                </div>
            </div>
        </div>
    );

    // ================== (4) DETAILS ==================
    const detailsSection = !isLoading && tripDetail && (
        <div className="space-y-4 text-xs border-t pt-3">
            {/* Section: Shipment */}
            <div>
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutShipment') /* حول الشحنة */}</h3>
                <div className="space-y-1">
                    <DetailRow
                        label={t('tripDetails.trackingSerialNumber')}
                        value={tripDetail.tracker.serialNumber ?? 'N/A'}
                    />
                    <DetailRow
                        label={t('tripDetails.elockSerialNumber')}
                        value={tripDetail.eLocks[0]?.tripELockId ?? 'N/A'}
                    />
                    <DetailRow
                        label={t('tripDetails.shipmentDescription')}
                        value={tripDetail.shipmentDescription ?? 'N/A'}
                    />
                </div>
            </div>

            {/* Section: Vehicle */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutVehicle') /* حول المركبة */}</h3>
                <div className="flex gap-2 text-sm justify-between">
                    {/* Label */}
                    {/* <span className="font-medium text-gray-500 whitespace-nowrap">
                        {t('tripDetails.aboutVehicle') /* حول المركبة */}
                    {/* </span> */}
                    {/* Values */}
                    <span className="text-blue-400 truncate max-w-[160px] font-medium">
                        {[tripDetail.vehicle?.plateNo ?? '1234-XYZ', tripDetail.vehicle?.id ?? '544554'].join(' , ')}
                    </span>
                </div>
            </div>

            {/* Section: Driver */}
            <div className="border-t pt-3">
                <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.aboutDriver') /* حول السائق */}</h3>
                <div className="space-y-1">
                    <DetailRow label={t('tripDetails.driverName')} value={tripDetail.driver?.name ?? 'N/A'} />
                    <DetailRow
                        label={t('tripDetails.driverNationality')}
                        value={tripDetail.driver?.passportCountry ?? ''}
                    />
                </div>
            </div>
        </div>
    );

    // ================== (5) STATUS ==================
    const statusSection = !isLoading && tripDetail && (
        <div className="border-t pt-2 text-xs w-107">
            <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.status')}</h3>

            <div className="flex flex-wrap gap-2">
                {statusConditions.map(({ id, value, bgColor, textColor, icon, tooltipKey }) => (
                    <Tooltip key={id} tooltipMessage={t(tooltipKey)}>
                        <TooltipTrigger asChild>
                            <Tag
                                bgColor={bgColor}
                                textColor={textColor}
                                icon={icon}
                                className="inline-flex max-w-max cursor-pointer">
                                {value}
                            </Tag>
                        </TooltipTrigger>
                    </Tooltip>
                ))}
            </div>
        </div>
    );

    // ================== (6) ALERTS ==================
    const alertsTagSection = !isLoading && (
        <div className="border-t pt-3">
            <h3 className="font-semibold text-gray-800 mb-2">{t('tripDetails.alerts') /* Alerts */}</h3>

            {tripDetail?.activeAlerts && tripDetail.activeAlerts.length > 0 ? (
                <div className="flex gap-2 flex-wrap">
                    {tripDetail.activeAlerts.map((alert, idx) => (
                        <Tag key={idx} bgColor="bg-red-100" textColor="text-red-700" icon="alert">
                            {localized(alert.alertType.name)}
                        </Tag>
                    ))}
                </div>
            ) : (
                <p className="text-gray-500 text-xs">{t('tripDetails.noAlerts')}</p>
            )}
        </div>
    );

    return (
        <BaseInfoWindow
            position={position}
            iconName="truck-2"
            title={title}
            subtitle={subtitle}
            onClose={onClose}
            headerColor="bg-green-700">
            <div className="flex flex-col h-[500px]">
                {/* Content (scrollable) */}
                <div className="flex-1 overflow-y-auto space-y-3 pr-1">
                    {alertsSection}
                    {fromToSection}
                    {detailsSection}
                    {statusSection}
                    {alertsTagSection}
                </div>

                {/* Footer (sticky) */}
                <div className=" pt-2 mt-2 bg-white sticky bottom-0">
                    <div className="flex justify-end items-center border-t pt-2">
                        <Button className="flex items-center gap-2 px-3 py-1 border border-green-700 text-green-700 bg-white rounded text-sm shadow-sm">
                            <span>
                                <Info size={12} className="text-green-700" />
                            </span>
                            {t('tripDetails.tripDetails')}
                        </Button>
                    </div>
                </div>
            </div>
        </BaseInfoWindow>
    );
}
