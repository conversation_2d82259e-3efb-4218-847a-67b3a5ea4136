import { useEffect, useState } from 'react';
import { APIProvider, ControlPosition, Map, MapControl, Marker } from '@vis.gl/react-google-maps';

import { useUIStore } from '@/stores/ui.store';
import { appConfig } from '@/shared/config/app-settings.config';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import type { MapPointDataItem } from '@/infrastructure/api/map-points/types';
import type { PortItem } from '@/infrastructure/api/ports/types';
import type { TripLocationItem } from '@/infrastructure/api/trips-location/types';
import { useDisplaySettingsStore } from '@/stores/display-settings.store';
import { useTripLocationStore } from '@/stores/trip-location.store';

import { ClusteredTripMarkers } from './ClusteredTripMarkers';
import { RulerTool } from './RulerTool';
import { MapCameraFollower } from './MapCameraFollower';
import { TripMarker } from './TripMarker';
import CheckPointInfoWindow from './info-window/CheckPointInfoWindow';
import { TripDetailsInfoWindow } from './info-window/TripDetailsInfoWindow';
import { PortInfoWindow } from './info-window/PortInfoWindow';

/**
 * Extended map point types with icon property for display
 */
export type MapPoint = MapPointDataItem & { icon: string };
export type Mapport = PortItem & { icon: string };
export type MapTrip = TripLocationItem & { icon: string };

type ActiveEntity =
    | { type: 'trip'; data: MapTrip }
    | { type: 'point'; data: MapPoint }
    | { type: 'port'; data: Mapport }
    | null;

/**
 * Props for GoogleMap component.
 *
 * @property center - Optional initial map center coordinates.
 * @property zoom - Optional initial zoom level.
 * @property height - CSS height of the map container.
 * @property width - CSS width of the map container.
 * @property points - Array of points to render as markers.
 * @property ports - Array of ports to render as markers.
 * @property tripLocations - Array of trip locations to render as markers or clusters.
 */
export interface GoogleMapProps {
    center?: { lat: number; lng: number };
    zoom?: number;
    height?: string;
    width?: string;
    points?: MapPoint[];
    ports?: Mapport[];
    tripLocations?: MapTrip[];
    children?: React.ReactNode;
}

/**
 * GoogleMap
 *
 * Renders a fully-featured Google Map using @vis.gl/react-google-maps.
 * Supports:
 *  - Rendering simple points and ports as individual markers.
 *  - Rendering trip locations either as clustered markers or individual markers.
 *  - Ruler tool for measuring distances.
 *
 * Props:
 *  - `height` / `width` define container dimensions.
 *  - `points`, `ports`, `tripLocations` are arrays of objects with coordinates and icons.
 */
export function GoogleMap({
    height = '100%',
    width = '100%',
    points = [],
    ports = [],
    tripLocations = [],
    children,
}: GoogleMapProps) {
    const [googleLoaded, setGoogleLoaded] = useState(false);
    const localized = useLocalized();
    // One unified state for all popovers
    // UI store: read activeInfoWindow and setActiveInfoWindow
    const activeInfoWindow = useUIStore((s) => s.activeInfoWindow);
    const setActiveInfoWindow = useUIStore((s) => s.setActiveInfoWindow);

    // UI store state & actions
    const activeTab = useUIStore((s) => s.activeTab);
    const setActiveTab = useUIStore((s) => s.setActiveTab);

    // Helper to open info windows: avoid redundant writes
    const openPointInfoWindow = (point: MapPoint) => {
        if (activeInfoWindow && activeInfoWindow.kind === 'point' && activeInfoWindow.id === point.id) return;
        setActiveInfoWindow({ kind: 'point', id: point.id });
    };
    const openPortInfoWindow = (port: Mapport) => {
        if (activeInfoWindow && activeInfoWindow.kind === 'port' && activeInfoWindow.id === port.id) return;
        setActiveInfoWindow({ kind: 'port', id: port.id });
    };
    const openTripInfoWindow = (trip: MapTrip) => {
        if (activeInfoWindow && activeInfoWindow.kind === 'trip' && activeInfoWindow.id === trip.id) return;
        setActiveInfoWindow({ kind: 'trip', id: trip.id });
    };

    // Convert store activeInfoWindow into actual entity for rendering
    const activeEntity: ActiveEntity | null = (() => {
        if (!activeInfoWindow) return null;
        const { kind, id } = activeInfoWindow;
        if (kind === 'point') {
            const found = points.find((p) => String(p.id) === String(id));
            return found ? { type: 'point', data: found } : null;
        }
        if (kind === 'port') {
            const found = ports.find((p) => String(p.id) === String(id));
            return found ? { type: 'port', data: found } : null;
        }
        if (kind === 'trip') {
            const found = tripLocations.find((t) => String(t.id) === String(id));
            return found ? { type: 'trip', data: found } : null;
        }
        return null;
    })();

    const highlightedTripId = useTripLocationStore((s) => s.highlightedTripId);

    // Auto open InfoWindow if trip is highlighted
    useEffect(() => {
        if (highlightedTripId) {
            const trip = tripLocations.find((t) => String(t.id) === String(highlightedTripId));
            if (trip) {
                setActiveInfoWindow({ kind: 'trip', id: trip.id });
            }
        }
    }, [highlightedTripId, tripLocations, setActiveInfoWindow]);

    // Trip display mode: "cluster" or "individual"
    const tripDisplayMode = useDisplaySettingsStore((s) => s.settings.tripDisplayMode);
    return (
        <div style={{ height, width }}>
            <APIProvider apiKey={appConfig.get('googleMapsApiKey')} libraries={['geometry', 'marker']}>
                <Map
                    mapId={appConfig.get('googleMapId')}
                    defaultCenter={appConfig.get('googleDefaultMapCenter')}
                    defaultZoom={appConfig.get('googleDefaultMapZoom')}
                    minZoom={3}
                    style={{ width: '100%', height: '100%' }}
                    onTilesLoaded={() => setGoogleLoaded(true)}
                    onClick={() => {
                        // Per business rules: clicking map background only closes InfoWindow
                        if (activeInfoWindow !== null) setActiveInfoWindow(null);
                        if (activeTab !== null) setActiveTab(null);
                    }}>
                    {googleLoaded && (
                        <>
                            {/* Render standard points */}
                            {points.map((point) => (
                                <Marker
                                    key={`point-${point.id}`}
                                    position={{ lat: point.lat, lng: point.long }}
                                    title={localized(point.name)}
                                    icon={{ url: point.icon, scaledSize: new google.maps.Size(32, 32) }}
                                    onClick={() => openPointInfoWindow(point)}
                                />
                            ))}

                            {/* Render ports */}
                            {ports.map((port) => (
                                <Marker
                                    key={`port-${port.id}`}
                                    position={{ lat: port.lat, lng: port.long }}
                                    title={localized(port.name)}
                                    icon={{ url: port.icon, scaledSize: new google.maps.Size(32, 32) }}
                                    onClick={() => openPortInfoWindow(port)}
                                />
                            ))}

                            {/* Render trip locations */}
                            {tripDisplayMode === 'cluster' && (
                                <ClusteredTripMarkers
                                    trips={tripLocations}
                                    onTripClick={(trip) => openTripInfoWindow(trip)}
                                />
                            )}
                            {tripDisplayMode === 'individual' &&
                                tripLocations.map((trip) => (
                                    <TripMarker key={trip.id} trip={trip} onClick={() => openTripInfoWindow(trip)} />
                                ))}

                            {/* ✅ Single InfoWindow */}
                            {activeEntity?.type === 'point' && (
                                <CheckPointInfoWindow
                                    point={activeEntity.data}
                                    onClose={() => {
                                        // avoid redundant write
                                        if (activeInfoWindow) setActiveInfoWindow(null);
                                    }}
                                />
                            )}
                            {activeEntity?.type === 'trip' && (
                                <TripDetailsInfoWindow
                                    trip={activeEntity.data}
                                    onClose={() => {
                                        // avoid redundant write
                                        if (activeInfoWindow) setActiveInfoWindow(null);
                                    }}
                                />
                            )}
                            {activeEntity?.type === 'port' && (
                                <PortInfoWindow
                                    port={activeEntity.data}
                                    onClose={() => {
                                        // avoid redundant write
                                        if (activeInfoWindow) setActiveInfoWindow(null);
                                    }}
                                />
                            )}

                            {/* Camera follower */}
                            <MapCameraFollower trips={tripLocations} />
                        </>
                    )}

                    <MapControl position={ControlPosition.LEFT_TOP}>
                        <RulerTool />
                    </MapControl>
                    {/* ✅ Your menu becomes a real Google Maps control, so it survives fullscreen */}
                    {children && <MapControl position={ControlPosition.TOP_RIGHT}>{children}</MapControl>}
                </Map>
            </APIProvider>
        </div>
    );
}
