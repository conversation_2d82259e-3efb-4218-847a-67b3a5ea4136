import { useTranslation } from 'react-i18next';
import { HiLockClosed } from 'react-icons/hi2';
import { IoMdCheckmarkCircleOutline } from 'react-icons/io';
import { IoArrowDownOutline } from 'react-icons/io5';
import { Md<PERSON><PERSON><PERSON>, MdFilterListAlt, MdNotificationsActive, MdOutlineNotificationsOff } from 'react-icons/md';
import { RiArrowLeftUpLine } from 'react-icons/ri';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { TbBatteryVertical } from 'react-icons/tb';
import { useState } from 'react';
import { useEffect } from 'react';

import { Button } from '@/components/common/ui/Button';
import Tooltip from '@/components/common/ui/Tooltip';
import SummaryCard from '@/components/common/ui/SummaryCard';
import type { SummaryCardDetails } from '@/components/common/ui/SummaryCard';
import { useTripFiltersStore } from '@/stores/trip-filters.store';
import type { TripsRequest } from '@/infrastructure/api/trips/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { useTripsStore } from '@/stores/trips.store';

import MyPortsDetails from '../features/my-ports/MyPortsDetails';
import TripFilterDialog from '../common/trip-filter-dialog/TripFilterDialog';
import { Icon } from '../common/ui/Icon';

// import { useTripsStore } from '@/stores/trips.store';

const TRIP_STATUS_OPTIONS = [
    { label: 'insideEntryPort', value: 1 },
    { label: 'onRoute', value: 2 },
    { label: 'inExitPort', value: 3 },
] as const;
export default function MyPorts() {
    const { t } = useTranslation();
    const { t: tWarnings } = useTranslation(['warnings']);

    const [visible, setVisible] = useState(false);

    const applyFilter = () => {
        // loadTrips(filters as TripsRequest); // TODO: implement this after the "feat(my-ports): integrate trips data with DataTable" branch is merged
        setVisible(false);
    };

    const { i18n } = useTranslation();
    const trips = useTripsStore((state) => state.trips);
    const loadTrips = useTripsStore((state) => state.loadTrips);
    const localized = useLocalized();

    // todo: trigger when loading user filters from cache
    useEffect(() => {
        loadTrips({
            pageSize: 1000,
        });
    }, [loadTrips]);

    useTripFiltersStore.subscribe((state) => {
        loadTrips(state.filters as TripsRequest);
    });

    const summaryCardsDetails: SummaryCardDetails[] = [
        {
            title: t('common.activeTripsWithAlerts'),
            icon: <MdNotificationsActive />,
            value: 1500,
        },
        {
            title: t('common.activeTripsWithoutAlerts'),
            icon: <MdOutlineNotificationsOff />,
            value: 12,
        },
        {
            title: t('common.totalActiveTrips'),
            icon: <IoMdCheckmarkCircleOutline />,
            value: 1350,
        },
        { title: t('common.totalClosedTrips'), icon: <HiLockClosed />, value: 18 },
    ];

    return (
        <>
            <div className="flex flex-col pt-4">
                <div className="flex items-center justify-between flex-wrap px-4 gap-3">
                    <div className="flex gap-3 mb-4 flex-grow-1">
                        {summaryCardsDetails.map((item, index) => (
                            <SummaryCard details={item} key={index} />
                        ))}
                    </div>

                    <div className="flex flex-wrap justify-end gap-3 pe-3 flex-grow-1 mb-4">
                        <Button
                            size="sm"
                            variant="secondary"
                            className="border border-blue-600 text-blue-600"
                            onClick={() => setVisible(true)}>
                            <MdFilterListAlt />
                            {t('common.filter')}
                        </Button>
                        <TripFilterDialog onApply={applyFilter} open={visible} onOpenChange={setVisible} />
                    </div>
                </div>

                <div className="_table_container flex-grow-1">
                    <DataTable
                        key={i18n.language} // Force re-render when language changes
                        value={trips}
                        tableStyle={{ minWidth: '50rem' }}
                        scrollable
                        scrollHeight="72vh"
                        paginator
                        rows={9}
                        stripedRows
                        className="shadow-md  border border-gray-200"
                        dir="rtl">
                        <Column
                            header={t('common.details')}
                            style={{ width: '5rem', textAlign: 'center' }}
                            body={() => <MyPortsDetails />}
                            headerClassName="bg-gray-100 font-semibold text-sm"
                        />

                        <Column
                            body={(rowData) => rowData.transitNumber}
                            header={t('common.transitNumber')}
                            style={{ width: '8rem', textAlign: 'center' }}
                            headerClassName="bg-gray-100 font-semibold text-sm"
                        />

                        <Column
                            body={(rowData) => rowData.shipmentDescription}
                            header={t('common.shipmentDescription')}
                            style={{ width: '15rem' }}
                            headerClassName="bg-gray-100 font-semibold text-sm"
                        />

                        <Column
                            body={(rowData) => (
                                <>
                                    <p className="flex items-center  justify-center gap-1 mb-1 whitespace-nowrap border-b-4 border-gray-300">
                                        <IoArrowDownOutline className="mt-1 size-4 text-blue-600" />
                                        {localized(rowData.entryPort.name)}
                                    </p>
                                    <p className="flex items-center justify-center gap-1 mb-1 whitespace-nowrap">
                                        <RiArrowLeftUpLine className="mt-1 size-4 text-red-600" />
                                        {localized(rowData.exitPort.name)}
                                    </p>
                                </>
                            )}
                            style={{ width: '15rem' }}
                            header={`${t('common.portIn')} - ${t('common.portOut')}`}
                        />

                        <Column
                            body={(rowData) => {
                                const status = TRIP_STATUS_OPTIONS.find((s) => s.value === rowData.lastKnownRouteZone);
                                return t(`filter.${status?.label}`) || '-';
                            }}
                            header={t('common.latestInformationAboutTheTarget')}
                            style={{ width: '20rem' }}
                        />

                        <Column
                            body={(rowData) => (
                                <>
                                    <p className="border-b-4 border-gray-300">{rowData.tracker.id}</p>
                                    <p>{rowData.tracker.serialNumber}</p>
                                    {rowData.vehicle.plateNo && (
                                        <p className="text-xs text-gray-500">{rowData.vehicle.plateNo}</p>
                                    )}
                                </>
                            )}
                            header={t('common.tracker')}
                            style={{ width: '8rem' }}
                        />

                        <Column
                            body={(rowData) => (
                                <>
                                    <p className="font-medium">{rowData.driver.name}</p>
                                    <p className="text-xs text-gray-500">{rowData.driver.mobileNo}</p>
                                </>
                            )}
                            header={t('common.driver')}
                            style={{ width: '10rem' }}
                        />

                        <Column
                            body={(rowData) => (
                                <>
                                    <p>{rowData.vehicle.plateNo}</p>
                                    <p className="text-xs text-gray-600">
                                        {rowData.vehicle.model}, {rowData.vehicle.color},
                                        {rowData.vehicle.plateCountryName}
                                    </p>
                                    <p className="text-xs text-gray-500">{rowData.truck?.plateCountryName}</p>
                                </>
                            )}
                            header={t('common.truck')}
                            style={{ width: '14rem' }}
                        />

                        <Column
                            body={(rowData) => (
                                <span
                                    className={`px-2 py-1 rounded-lg text-xs ${
                                        // localized(rowData.activeAlert.alertType.name)
                                        rowData.activeAlert ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'
                                    }`}>
                                    {rowData.activeAlert
                                        ? localized(rowData.activeAlert.alertType.name)
                                        : tWarnings('noWarnings')}
                                </span>
                            )}
                            header={t('common.warnings')}
                            style={{ width: '10rem', textAlign: 'center' }}
                        />

                        <Column
                            body={() => (
                                <div className="flex gap-2 items-center justify-center">
                                    <Tooltip tooltipMessage="filter.active">
                                        <MdCheck className="text-green-600 size-5" />
                                    </Tooltip>

                                    <Tooltip tooltipMessage="common.batteryLife" translationParams={{ value: '0%' }}>
                                        <TbBatteryVertical className="size-5 text-gray-600" />
                                    </Tooltip>

                                    <Tooltip tooltipMessage="common.noCharging">
                                        <Icon name="charger" />
                                    </Tooltip>
                                </div>
                            )}
                            header={t('common.status')}
                            style={{ width: '10rem', textAlign: 'center' }}
                        />
                    </DataTable>
                </div>
            </div>
        </>
    );
}
