/* eslint-disable @typescript-eslint/no-explicit-any */
import './Navbar.css';
import { useTranslation } from 'react-i18next';
import { Link, NavLink, useLocation } from 'react-router-dom';
import { FaCaretDown } from 'react-icons/fa';
import {
    <PERSON>c<PERSON>d<PERSON><PERSON>,
    FcBusinessman,
    FcComboChart,
    FcExpired,
    FcGenealogy,
    FcGlobe,
    FcInspection,
    FcNews,
    FcViewDetails,
} from 'react-icons/fc';

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/common/ui/DropdownMenu';

export default function NavbarItems() {
    const { t } = useTranslation();
    const location = useLocation();

    // Check if current path starts with /reports
    const isReportsActive = location.pathname.startsWith('/reports');

    // Helper function to check if a specific report route is active
    const isReportRouteActive = (route: string) => location.pathname === route;

    return (
        <div className="nav-items-container">
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="">
                {' '}
                {t('navbar.settings')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/monitor-board">
                {' '}
                {t('navbar.monitoring')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="">
                {' '}
                {t('navbar.trips')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/dashboard">
                {' '}
                {t('navbar.dashboard')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="">
                {' '}
                {t('navbar.arrivalTracking')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="/my-ports">
                {' '}
                {t('navbar.myPorts')}{' '}
            </NavLink>
            <NavLink className={`item ${(isActive: boolean) => (isActive ? 'active' : '')}`} to="">
                {' '}
                {t('navbar.ports')}{' '}
            </NavLink>

            <DropdownMenu>
                <DropdownMenuTrigger
                    className={`flex items-center gap-2 outline-0 item ${isReportsActive ? 'active' : ''}`}>
                    <div className="flex items-center gap-3">
                        {t('navbar.reports')}
                        <FaCaretDown className="text-[#aaaaaa]" />
                    </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    side="bottom"
                    sideOffset={12}
                    className="w-120 grid grid-cols-3 gap-x-3 gap-y-1 p-3 _effect">
                    <DropdownMenuItem
                        className={`report-item cursor-pointer ${isReportRouteActive('/reports/trips') ? 'active-child' : ''}`}>
                        <FcGlobe />
                        {t('reports.trips')}
                    </DropdownMenuItem>

                    <NavLink to="/reports/alerts" className={`${(isActive: any) => (isActive ? 'active' : '')} `}>
                        <DropdownMenuItem
                            className={`report-item cursor-pointer ${isReportRouteActive('/reports/alerts') ? 'active-child' : ''}`}>
                            <div className="flex items-center gap-3 ">
                                <FcAdvertising />
                                {t('reports.alerts')}
                            </div>
                        </DropdownMenuItem>
                    </NavLink>

                    <DropdownMenuItem
                        className={`report-item cursor-pointer ${isReportRouteActive('/reports/records') ? 'active-child' : ''}`}>
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcViewDetails />
                                {t('reports.records')}
                            </div>
                        </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem className="report-item cursor-pointer">
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcComboChart />
                                {t('reports.statistics')}
                            </div>
                        </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem className="report-item cursor-pointer">
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcExpired />
                                {t('reports.stops')}
                            </div>
                        </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem className="report-item cursor-pointer">
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcBusinessman />
                                {t('reports.employees')}
                            </div>
                        </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem className="report-item cursor-pointer">
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcGenealogy />
                                {t('reports.portDistribution')}
                            </div>
                        </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem className="report-item cursor-pointer">
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcNews />
                                {t('reports.tripTracking')}
                            </div>
                        </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem className="report-item cursor-pointer">
                        <Link to="" className=" item">
                            <div className="flex items-center gap-3">
                                <FcInspection />
                                {t('reports.activeTrips')}
                            </div>
                        </Link>
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
}
