import React from 'react';
import type { Preview } from '@storybook/react-vite';
import '../src/index.css'; // Tailwind + global styles
import 'primereact/resources/primereact.min.css';
import './preview.css'; // Storybook-specific styles
import 'virtual:svg-icons-register'; // Import SVG sprite for icons
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '../src/shared/config/i18n';

const queryClient = new QueryClient();

const preview: Preview = {
    parameters: {
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/i,
            },
        },
        a11y: {
            test: 'todo',
        },
    },
    decorators: [
        (Story) => (
            <I18nextProvider i18n={i18n}>
                <QueryClientProvider client={queryClient}>
                    <Story />
                </QueryClientProvider>
            </I18nextProvider>
        ),
    ],
};

export default preview;
