import type { StorybookConfig } from '@storybook/react-vite';
import path from 'path';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

const config: StorybookConfig = {
    stories: ['../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
    addons: [
        '@chromatic-com/storybook',
        '@storybook/addon-docs',
        '@storybook/addon-onboarding',
        '@storybook/addon-a11y',
        '@storybook/addon-vitest',
    ],
    framework: {
        name: '@storybook/react-vite',
        options: {},
    },
    viteFinal: async (config) => {
        if (config.resolve) {
            config.resolve.alias = {
                ...config.resolve.alias,
                '@': path.resolve(__dirname, '../src'),
                '@imgs': path.resolve(__dirname, '../src/assets/imgs'),
            };
        }

        // Add SVG icons plugin
        if (config.plugins) {
            config.plugins.push(
                createSvgIconsPlugin({
                    iconDirs: [path.resolve(process.cwd(), 'src/assets/imgs/svg')],
                    symbolId: 'icon-[name]',
                    svgoOptions: {
                        multipass: true,
                        plugins: [
                            {
                                name: 'removeAttrs',
                                params: { attrs: '(fill|stroke|style)' },
                            },
                        ],
                    },
                }),
            );
        }

        return config;
    },
};
export default config;
